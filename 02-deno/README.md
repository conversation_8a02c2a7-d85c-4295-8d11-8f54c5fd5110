# Guide OCR - Deno Edition

A powerful TypeScript/Deno application for OCR processing of PDF business listings using Google Gemini AI. This tool can analyze PDF documents, identify data columns, extract structured business information, and export results to Excel format.

## Features

- 🔍 **Smart Column Analysis**: Analyzes sample pages to automatically identify potential data columns
- 📄 **Batch PDF Processing**: Converts PDF pages to images and processes them efficiently
- 🤖 **AI-Powered Extraction**: Uses Google Gemini AI for intelligent OCR and data extraction
- 🔗 **Multi-Page Entry Handling**: Intelligently handles business listings that span multiple pages
- 📊 **Excel Export**: Creates well-formatted Excel files with metadata and statistics
- 🛠️ **Modern TypeScript**: Built with latest TypeScript features and Deno runtime
- 📝 **Comprehensive Logging**: Detailed logging and error handling

## Prerequisites

- [Deno](https://deno.land/) v1.40.0 or higher
- Google Gemini API key
- PDF file to process
- System dependencies:
  - `pdfinfo` (from poppler-utils) for PDF page counting
  - ImageMagick or similar for image processing (handled by pdf2pic)

## Installation

1. **Clone or download the project**
   ```bash
   cd 02-deno
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

3. **Edit `.env` file with your configuration**
   ```bash
   # Required: Your Google Gemini API key
   GEMINI_API_KEY=your_gemini_api_key_here

   # Optional: Customize paths and settings
   PDF_PATH=./pdf/list.pdf
   OUTPUT_DIR=./output
   TEMP_DIR=./temp
   SAMPLE_PAGES=5
   MAX_PAGES_PER_BATCH=10
   ```

4. **Verify setup**
   ```bash
   deno task setup
   ```

## Usage

### Quick Start (Full Pipeline)

Run the complete OCR pipeline in one command:

```bash
deno task start full
```

This will:
1. Analyze sample pages to identify columns
2. Extract data from all pages
3. Combine results and export to Excel

### Step-by-Step Usage

#### 1. Analyze Columns

Analyze random pages to identify potential data columns:

```bash
deno task analyze
# or with custom sample size
deno task start analyze --pages 10
```

This creates a JSON file with identified columns like:
- Business name
- Contact information (phone, email)
- Addresses (company, mailing)
- Social media accounts
- Categories and specialties
- And more...

#### 2. Convert PDF to Images (Optional)

Convert all PDF pages to PNG images for faster processing:

```bash
deno task convert
# or with custom options
deno task start convert --batch-size 5 --start-page 1 --end-page 50
```

This converts PDF pages to PNG images with:
- 50% reduced resolution (150 DPI vs 300 DPI) for smaller file sizes
- Skips pages that are already converted
- Retry logic with detailed error reporting

#### 3. Extract Data

Extract data from all PDF pages using the identified columns:

```bash
deno task extract
# or with custom batch size
deno task start extract --batch-size 5
```

This processes all pages and saves individual JSON files for each page.

#### 4. Combine and Export

Combine all extracted data and export to Excel:

```bash
deno task combine
# or with custom filename
deno task start combine --filename my_listings.xlsx
```

This creates a comprehensive Excel file with:
- Main data sheet with all business listings
- Metadata sheet with processing information
- Statistics sheet with analysis metrics

### Available Commands

```bash
# Run full pipeline
deno task start full [options]

# Individual steps
deno task analyze          # Analyze columns
deno task convert          # Convert PDF pages to PNG
deno task extract          # Extract data
deno task combine          # Combine and export

# Utility commands
deno task setup           # Validate configuration
deno task clean           # Clean temporary files
deno task lint            # Lint code
deno task fmt             # Format code
deno task check           # Type check
```

### Command Options

- `--pages <number>`: Number of sample pages to analyze (default: 5)
- `--batch-size <number>`: Pages to process per batch (default: 10)
- `--start-page <number>`: Starting page number for conversion (default: 1)
- `--end-page <number>`: Ending page number for conversion (default: last page)
- `--filename <string>`: Output Excel filename (default: business_listings.xlsx)

## Project Structure

```
02-deno/
├── src/
│   ├── services/          # Core services
│   │   ├── pdf.ts         # PDF processing
│   │   ├── gemini.ts      # Gemini AI integration
│   │   └── excel.ts       # Excel export
│   ├── utils/             # Utility functions
│   │   ├── logger.ts      # Logging system
│   │   └── file.ts        # File operations
│   ├── analyze.ts         # Column analysis utility
│   ├── convert.ts         # PDF to PNG conversion utility
│   ├── extract.ts         # Data extraction utility
│   ├── combine.ts         # Data combination utility
│   ├── config.ts          # Configuration management
│   ├── types.ts           # TypeScript interfaces
│   └── main.ts            # CLI interface
├── output/                # Generated output files
│   ├── analysis/          # Column analysis results
│   ├── converted/         # PDF conversion results
│   ├── extracted/         # Page-by-page extractions
│   └── combined/          # Final combined data
├── temp/                  # Temporary image files
├── logs/                  # Application logs
├── pdf/                   # PDF files to process
├── deno.json             # Deno configuration
└── .env                  # Environment variables
```

## Output Files

### Analysis Results
- `output/analysis/latest.json`: Latest column analysis
- `output/analysis/column_analysis_[timestamp].json`: Timestamped analyses

### Conversion Results
- `output/converted/convert_result_[timestamp].json`: PDF conversion statistics
- `temp/page_*.png`: Individual page PNG files

### Extraction Results
- `output/extracted/page_001.json`: Individual page extractions
- Each file contains listings found on that page

### Combined Results
- `output/combined/latest.json`: Latest combined data
- `output/combined/combined_data_[timestamp].json`: Timestamped combinations
- `output/business_listings.xlsx`: Final Excel export

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GEMINI_API_KEY` | Google Gemini API key | Required |
| `PDF_PATH` | Path to PDF file | `./pdf/list.pdf` |
| `OUTPUT_DIR` | Output directory | `./output` |
| `TEMP_DIR` | Temporary files directory | `./temp` |
| `SAMPLE_PAGES` | Pages to analyze for columns | `5` |
| `MAX_PAGES_PER_BATCH` | Batch size for processing | `10` |
| `GEMINI_MODEL` | Gemini model to use | `gemini-1.5-flash` |
| `GEMINI_TEMPERATURE` | AI temperature (0-2) | `0.1` |
| `DEBUG` | Enable debug logging | `false` |

### Deno Configuration

The `deno.json` file includes:
- Import maps for dependencies
- TypeScript compiler options
- Linting and formatting rules
- Task definitions

## Error Handling

The application includes comprehensive error handling:
- Graceful failures for individual pages
- Retry logic for API calls
- Detailed error logging
- Partial results preservation

## Performance Considerations

- **Batch Processing**: Pages are processed in configurable batches
- **Rate Limiting**: Built-in delays to respect API limits
- **Memory Management**: Temporary files are cleaned up automatically
- **Parallel Processing**: Multiple pages can be processed concurrently

## Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY not set"**
   - Ensure you've copied `.env.example` to `.env`
   - Add your valid Gemini API key

2. **"PDF file not found"**
   - Check the `PDF_PATH` in your `.env` file
   - Ensure the PDF file exists at the specified location

3. **"pdfinfo command not found"**
   - Install poppler-utils: `brew install poppler` (macOS) or `apt-get install poppler-utils` (Ubuntu)

4. **Memory issues with large PDFs**
   - Reduce `MAX_PAGES_PER_BATCH` in `.env`
   - Increase system memory or use a machine with more RAM

### Debug Mode

Enable debug logging:
```bash
DEBUG=true deno task start [command]
```

### Logs

Check application logs in the `logs/` directory:
- `app-[date].log`: Daily log files with detailed information

## Contributing

1. Follow the existing code style
2. Run `deno task lint` and `deno task fmt` before committing
3. Add tests for new functionality
4. Update documentation as needed

## License

MIT License - see LICENSE file for details
