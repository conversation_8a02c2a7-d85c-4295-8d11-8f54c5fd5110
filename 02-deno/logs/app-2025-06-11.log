2025-06-11T21:51:20.572Z [DEBUG] Environment variables loaded
2025-06-11T21:51:20.573Z [INFO] Configuration validation passed
2025-06-11T21:51:26.009Z [INFO] Initializing Analyze Utility
2025-06-11T21:51:26.010Z [DEBUG] Environment variables loaded
2025-06-11T21:51:26.010Z [INFO] Configuration validation passed
2025-06-11T21:51:26.010Z [ERROR] Failed to get total pages from PDF | Data: {}
2025-06-11T21:52:44.500Z [INFO] Initializing Analyze Utility
2025-06-11T21:52:44.501Z [DEBUG] Environment variables loaded
2025-06-11T21:52:44.501Z [INFO] Configuration validation passed
2025-06-11T21:52:44.502Z [ERROR] Failed to get total pages from PDF | Data: {}
2025-06-11T21:55:18.773Z [INFO] Initializing Analyze Utility
2025-06-11T21:55:18.774Z [DEBUG] Environment variables loaded
2025-06-11T21:55:18.774Z [INFO] Configuration validation passed
2025-06-11T21:55:18.826Z [INFO] PDF has 258 pages
2025-06-11T21:55:18.826Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-11T21:55:18.826Z [INFO] Starting column analysis process
2025-06-11T21:55:18.826Z [INFO] Analyzing 5 random pages: 14, 52, 86, 114, 162
2025-06-11T21:55:18.826Z [INFO] PROGRESS: Converting sample pages to images
2025-06-11T21:55:18.827Z [INFO] PROGRESS: Converting page 14 to image
2025-06-11T21:55:25.616Z [DEBUG] Page 14 converted using ImageMagick
2025-06-11T21:55:26.023Z [DEBUG] Page 14 converted | Data: {"pageNumber":14,"imagePath":"temp/page_14.png","width":2480,"height":3508,"fileSize":5640187}
2025-06-11T21:55:26.023Z [INFO] PROGRESS: Converting page 52 to image
2025-06-11T21:55:31.933Z [DEBUG] Page 52 converted using ImageMagick
2025-06-11T21:55:31.934Z [DEBUG] Page 52 converted | Data: {"pageNumber":52,"imagePath":"temp/page_52.png","width":2480,"height":3508,"fileSize":6245233}
2025-06-11T21:55:31.934Z [INFO] PROGRESS: Converting page 86 to image
2025-06-11T21:55:39.888Z [DEBUG] Page 86 converted using ImageMagick
2025-06-11T21:55:39.891Z [DEBUG] Page 86 converted | Data: {"pageNumber":86,"imagePath":"temp/page_86.png","width":2480,"height":3508,"fileSize":6113585}
2025-06-11T21:55:39.891Z [INFO] PROGRESS: Converting page 114 to image
2025-06-11T21:55:45.933Z [DEBUG] Page 114 converted using ImageMagick
2025-06-11T21:55:45.934Z [DEBUG] Page 114 converted | Data: {"pageNumber":114,"imagePath":"temp/page_114.png","width":2480,"height":3508,"fileSize":6070908}
2025-06-11T21:55:45.934Z [INFO] PROGRESS: Converting page 162 to image
2025-06-11T21:55:51.733Z [DEBUG] Page 162 converted using ImageMagick
2025-06-11T21:55:51.733Z [DEBUG] Page 162 converted | Data: {"pageNumber":162,"imagePath":"temp/page_162.png","width":2480,"height":3508,"fileSize":5628536}
2025-06-11T21:55:51.734Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-11T21:55:51.734Z [INFO] Analyzing 5 images for column structure
2025-06-11T21:55:51.876Z [ERROR] Failed to analyze columns from images | Data: {}
2025-06-11T21:55:52.425Z [ERROR] Column analysis failed | Data: {}
2025-06-11T21:55:52.426Z [INFO] PDF service cleanup completed
2025-06-11T21:56:50.201Z [INFO] Initializing Analyze Utility
2025-06-11T21:56:50.202Z [DEBUG] Environment variables loaded
2025-06-11T21:56:50.203Z [INFO] Configuration validation passed
2025-06-11T21:56:50.253Z [INFO] PDF has 258 pages
2025-06-11T21:56:50.253Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-11T21:56:50.253Z [INFO] Starting column analysis process
2025-06-11T21:56:50.254Z [INFO] Analyzing 5 random pages: 133, 142, 147, 184, 214
2025-06-11T21:56:50.254Z [INFO] PROGRESS: Converting sample pages to images
2025-06-11T21:56:50.254Z [INFO] PROGRESS: Converting page 133 to image
2025-06-11T21:56:56.139Z [DEBUG] Page 133 converted using ImageMagick
2025-06-11T21:56:56.165Z [DEBUG] Page 133 converted | Data: {"pageNumber":133,"imagePath":"temp/page_133.png","width":2480,"height":3508,"fileSize":5791919}
2025-06-11T21:56:56.165Z [INFO] PROGRESS: Converting page 142 to image
2025-06-11T21:57:05.131Z [DEBUG] Page 142 converted using ImageMagick
2025-06-11T21:57:05.131Z [DEBUG] Page 142 converted | Data: {"pageNumber":142,"imagePath":"temp/page_142.png","width":2480,"height":3508,"fileSize":6725405}
2025-06-11T21:57:05.132Z [INFO] PROGRESS: Converting page 147 to image
2025-06-11T21:57:10.889Z [DEBUG] Page 147 converted using ImageMagick
2025-06-11T21:57:10.890Z [DEBUG] Page 147 converted | Data: {"pageNumber":147,"imagePath":"temp/page_147.png","width":2480,"height":3508,"fileSize":5735699}
2025-06-11T21:57:10.890Z [INFO] PROGRESS: Converting page 184 to image
2025-06-11T21:57:16.501Z [DEBUG] Page 184 converted using ImageMagick
2025-06-11T21:57:16.502Z [DEBUG] Page 184 converted | Data: {"pageNumber":184,"imagePath":"temp/page_184.png","width":2480,"height":3508,"fileSize":5749913}
2025-06-11T21:57:16.502Z [INFO] PROGRESS: Converting page 214 to image
2025-06-11T21:57:22.920Z [DEBUG] Page 214 converted using ImageMagick
2025-06-11T21:57:22.921Z [DEBUG] Page 214 converted | Data: {"pageNumber":214,"imagePath":"temp/page_214.png","width":2480,"height":3508,"fileSize":6817368}
2025-06-11T21:57:22.921Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-11T21:57:22.922Z [INFO] Analyzing 5 images for column structure
2025-06-11T21:57:24.970Z [ERROR] Failed to analyze columns from images | Data: {}
2025-06-11T21:57:24.970Z [ERROR] Column analysis failed | Data: {}
2025-06-11T21:57:24.971Z [INFO] PDF service cleanup completed
2025-06-11T21:58:33.995Z [INFO] Initializing Analyze Utility
2025-06-11T21:58:33.996Z [DEBUG] Environment variables loaded
2025-06-11T21:58:33.997Z [INFO] Configuration validation passed
2025-06-11T21:58:34.052Z [INFO] PDF has 258 pages
2025-06-11T21:58:34.052Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-11T21:58:34.052Z [INFO] Starting column analysis process
2025-06-11T21:58:34.053Z [INFO] Analyzing 5 random pages: 71, 164, 165, 202, 245
2025-06-11T21:58:34.053Z [INFO] PROGRESS: Converting sample pages to images
2025-06-11T21:58:34.053Z [INFO] PROGRESS: Converting page 71 to image
2025-06-11T21:58:41.111Z [DEBUG] Page 71 converted using ImageMagick
2025-06-11T21:58:41.139Z [DEBUG] Page 71 converted | Data: {"pageNumber":71,"imagePath":"temp/page_71.png","width":2480,"height":3508,"fileSize":5772253}
2025-06-11T21:58:41.139Z [INFO] PROGRESS: Converting page 164 to image
2025-06-11T21:58:46.748Z [DEBUG] Page 164 converted using ImageMagick
2025-06-11T21:58:46.748Z [DEBUG] Page 164 converted | Data: {"pageNumber":164,"imagePath":"temp/page_164.png","width":2480,"height":3508,"fileSize":5795323}
2025-06-11T21:58:46.749Z [INFO] PROGRESS: Converting page 165 to image
2025-06-11T21:58:52.511Z [DEBUG] Page 165 converted using ImageMagick
2025-06-11T21:58:52.512Z [DEBUG] Page 165 converted | Data: {"pageNumber":165,"imagePath":"temp/page_165.png","width":2480,"height":3508,"fileSize":5633007}
2025-06-11T21:58:52.512Z [INFO] PROGRESS: Converting page 202 to image
2025-06-11T21:58:58.701Z [DEBUG] Page 202 converted using ImageMagick
2025-06-11T21:58:58.702Z [DEBUG] Page 202 converted | Data: {"pageNumber":202,"imagePath":"temp/page_202.png","width":2480,"height":3508,"fileSize":6051514}
2025-06-11T21:58:58.702Z [INFO] PROGRESS: Converting page 245 to image
2025-06-11T21:59:04.544Z [DEBUG] Page 245 converted using ImageMagick
2025-06-11T21:59:04.545Z [DEBUG] Page 245 converted | Data: {"pageNumber":245,"imagePath":"temp/page_245.png","width":2480,"height":3508,"fileSize":5717228}
2025-06-11T21:59:04.545Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-11T21:59:04.545Z [INFO] Analyzing 5 images for column structure (attempt 1/3)
2025-06-11T21:59:11.538Z [WARN] Attempt 1 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from 10.99.1.223:60602 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (142.250.190.138:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-11T21:59:11.538Z [INFO] Retrying in 2000ms...
2025-06-11T21:59:13.541Z [INFO] Analyzing 5 images for column structure (attempt 2/3)
2025-06-11T21:59:15.584Z [WARN] Attempt 2 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from 10.99.1.223:60617 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (172.217.4.74:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-11T21:59:15.585Z [INFO] Retrying in 4000ms...
2025-06-11T21:59:19.587Z [INFO] Analyzing 5 images for column structure (attempt 3/3)
2025-06-11T21:59:22.008Z [WARN] Attempt 3 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from 10.99.1.223:60624 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (172.217.0.170:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-11T21:59:22.008Z [ERROR] Failed to analyze columns from images after all retries | Data: {}
2025-06-11T21:59:22.009Z [ERROR] Column analysis failed | Data: {}
2025-06-11T21:59:22.010Z [INFO] PDF service cleanup completed
2025-06-11T22:01:02.650Z [INFO] Initializing Analyze Utility
2025-06-11T22:01:02.651Z [DEBUG] Environment variables loaded
2025-06-11T22:01:02.651Z [INFO] Configuration validation passed
2025-06-11T22:01:02.703Z [INFO] PDF has 258 pages
2025-06-11T22:01:02.703Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-11T22:01:02.704Z [INFO] Starting column analysis process
2025-06-11T22:01:02.704Z [INFO] Analyzing 5 random pages: 97, 98, 186, 220, 258
2025-06-11T22:01:02.704Z [INFO] PROGRESS: Converting sample pages to images
2025-06-11T22:01:02.704Z [INFO] PROGRESS: Converting page 97 to image
2025-06-11T22:01:08.468Z [DEBUG] Page 97 converted using ImageMagick
2025-06-11T22:01:08.496Z [DEBUG] Page 97 converted | Data: {"pageNumber":97,"imagePath":"temp/page_97.png","width":2480,"height":3508,"fileSize":5618277}
2025-06-11T22:01:08.497Z [INFO] PROGRESS: Converting page 98 to image
2025-06-11T22:01:14.740Z [DEBUG] Page 98 converted using ImageMagick
2025-06-11T22:01:14.741Z [DEBUG] Page 98 converted | Data: {"pageNumber":98,"imagePath":"temp/page_98.png","width":2480,"height":3508,"fileSize":6082001}
2025-06-11T22:01:14.741Z [INFO] PROGRESS: Converting page 186 to image
2025-06-11T22:01:20.620Z [DEBUG] Page 186 converted using ImageMagick
2025-06-11T22:01:20.621Z [DEBUG] Page 186 converted | Data: {"pageNumber":186,"imagePath":"temp/page_186.png","width":2480,"height":3508,"fileSize":5816502}
2025-06-11T22:01:20.621Z [INFO] PROGRESS: Converting page 220 to image
2025-06-11T22:01:26.716Z [DEBUG] Page 220 converted using ImageMagick
2025-06-11T22:01:26.717Z [DEBUG] Page 220 converted | Data: {"pageNumber":220,"imagePath":"temp/page_220.png","width":2480,"height":3508,"fileSize":6342632}
2025-06-11T22:01:26.717Z [INFO] PROGRESS: Converting page 258 to image
2025-06-11T22:01:32.743Z [DEBUG] Page 258 converted using ImageMagick
2025-06-11T22:01:32.743Z [DEBUG] Page 258 converted | Data: {"pageNumber":258,"imagePath":"temp/page_258.png","width":2480,"height":3508,"fileSize":5727018}
2025-06-11T22:01:32.744Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-11T22:01:32.744Z [INFO] Analyzing 5 images for column structure (attempt 1/3)
2025-06-11T22:01:34.773Z [WARN] Attempt 1 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from 10.99.1.223:60771 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (172.217.4.74:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-11T22:01:34.774Z [INFO] Retrying in 2000ms...
2025-06-11T22:01:36.779Z [INFO] Analyzing 5 images for column structure (attempt 2/3)
2025-06-11T22:01:38.626Z [WARN] Attempt 2 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from 10.99.1.223:60776 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (172.217.0.170:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-11T22:01:38.626Z [INFO] Retrying in 4000ms...
2025-06-11T22:01:42.632Z [INFO] Analyzing 5 images for column structure (attempt 3/3)
2025-06-11T22:01:52.141Z [WARN] Attempt 3 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from 10.99.1.223:60782 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (142.250.191.234:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-11T22:01:52.142Z [ERROR] Failed to analyze columns from images after all retries | Data: {}
2025-06-11T22:01:52.143Z [ERROR] Column analysis failed | Data: {}
2025-06-11T22:01:52.147Z [INFO] PDF service cleanup completed
2025-06-11T22:02:23.288Z [INFO] Initializing Analyze Utility
2025-06-11T22:02:23.289Z [DEBUG] Environment variables loaded
2025-06-11T22:02:23.289Z [INFO] Configuration validation passed
2025-06-11T22:02:23.344Z [INFO] PDF has 258 pages
2025-06-11T22:02:23.345Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-11T22:02:23.345Z [INFO] Starting column analysis process
2025-06-11T22:02:23.345Z [INFO] Analyzing 5 random pages: 6, 34, 130, 176, 232
2025-06-11T22:02:23.346Z [INFO] PROGRESS: Converting sample pages to images
2025-06-11T22:02:23.346Z [INFO] PROGRESS: Converting page 6 to image
2025-06-11T22:02:29.384Z [DEBUG] Page 6 converted using ImageMagick
2025-06-11T22:02:29.410Z [DEBUG] Page 6 converted | Data: {"pageNumber":6,"imagePath":"temp/page_6.png","width":2480,"height":3508,"fileSize":5991147}
2025-06-11T22:02:29.410Z [INFO] PROGRESS: Converting page 34 to image
2025-06-11T22:02:35.015Z [DEBUG] Page 34 converted using ImageMagick
2025-06-11T22:02:35.015Z [DEBUG] Page 34 converted | Data: {"pageNumber":34,"imagePath":"temp/page_34.png","width":2480,"height":3508,"fileSize":5594747}
2025-06-11T22:02:35.015Z [INFO] PROGRESS: Converting page 130 to image
2025-06-11T22:02:41.022Z [DEBUG] Page 130 converted using ImageMagick
2025-06-11T22:02:41.023Z [DEBUG] Page 130 converted | Data: {"pageNumber":130,"imagePath":"temp/page_130.png","width":2480,"height":3508,"fileSize":6122308}
2025-06-11T22:02:41.023Z [INFO] PROGRESS: Converting page 176 to image
2025-06-11T22:02:49.782Z [DEBUG] Page 176 converted using ImageMagick
2025-06-11T22:02:49.783Z [DEBUG] Page 176 converted | Data: {"pageNumber":176,"imagePath":"temp/page_176.png","width":2480,"height":3508,"fileSize":7390153}
2025-06-11T22:02:49.783Z [INFO] PROGRESS: Converting page 232 to image
2025-06-11T22:02:56.329Z [DEBUG] Page 232 converted using ImageMagick
2025-06-11T22:02:56.330Z [DEBUG] Page 232 converted | Data: {"pageNumber":232,"imagePath":"temp/page_232.png","width":2480,"height":3508,"fileSize":5949824}
2025-06-11T22:02:56.331Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-11T22:02:56.331Z [INFO] Analyzing 5 images for column structure (attempt 1/3)
2025-06-11T22:03:27.248Z [INFO] SUCCESS: Identified 21 potential columns
2025-06-11T22:03:27.250Z [DEBUG] Identified columns | Data: [{"name":"Business Name","description":"The name of the business.","dataType":"string","required":true,"examples":["Dimmen Orange Calla","Dynamic Plant Culture","Eminent"]},{"name":"Category","description":"The industry or type of business.","dataType":"string","required":true,"examples":["breeder and exporter of zantedeschia","Greenhouse","growers"]},{"name":"Description","description":"A brief description of the business and its offerings.","dataType":"string","required":false,"examples":["breeder and exporter of zantedeschia, Leidschendam","tomato, sweet pepper, and chili pepper production/sales","GSP seed-producing – verkoop GSP-zaden – paprika’s en peper-planten"]},{"name":"Contact Person","description":"The name of a contact person at the business.","dataType":"string","required":false,"examples":["Frans de Bloois","Rodrigo Ruiz","John Fitch"]},{"name":"Phone","description":"The business's phone number.","dataType":"phone","required":true,"examples":["+**************","+52 55 574 3049","+**************"]},{"name":"Email","description":"The business's email address.","dataType":"email","required":true,"examples":["<EMAIL>","<EMAIL>","<EMAIL>"]},{"name":"Address","description":"The street address of the business.","dataType":"address","required":true,"examples":["Leidschendam","1, 30591 BALICAS (ES), Spain","Great Western Way, TAUNTON SOMERSETT TA2 6BX, United Kingdom"]},{"name":"Website","description":"The URL of the business's website.","dataType":"url","required":false,"examples":["www.emnent.nl","www.agrosolucionesag.com","www.growspan.co.uk"]},{"name":"Fax","description":"The business's fax number.","dataType":"phone","required":false,"examples":["+**************","+**************","+64 3 338 551 50"]},{"name":"Founded","description":"The year the business was founded.","dataType":"number","required":false,"examples":["1938","1996","1966"]},{"name":"Staff","description":"The number of employees at the business.","dataType":"number","required":false,"examples":["7","50","12"]},{"name":"Brand","description":"The brand or franchise the business is associated with.","dataType":"string","required":false,"examples":["Eminent","Ailux","MegaDome"]},{"name":"Social Media Accounts","description":"Links to the business's social media profiles.","dataType":"url","required":false,"examples":["https://www.facebook.com/HaydreamCommodities/","https://www.linkedin.com/in/nickboden/",""]},{"name":"Mailing Address","description":"The address for mailing purposes, if different from the business address.","dataType":"address","required":false,"examples":["P.O. Box 1600",""]},{"name":"Subsidaries","description":"List of subsidiary companies.","dataType":"string","required":false,"examples":["Harnois Inc",""]},{"name":"Certifications","description":"Any certifications or accreditations held by the business.","dataType":"string","required":false,"examples":["","",""]},{"name":"Service Areas","description":"The geographic areas served by the business.","dataType":"string","required":false,"examples":["","",""]},{"name":"Hours of Operation","description":"The business's hours of operation.","dataType":"string","required":false,"examples":["","",""]},{"name":"Specialties","description":"Specific areas of expertise or services offered.","dataType":"string","required":false,"examples":["","",""]},{"name":"Awards","description":"Any awards or recognitions received by the business.","dataType":"string","required":false,"examples":["","",""]},{"name":"Membership Information","description":"Details of any industry associations or memberships.","dataType":"string","required":false,"examples":["","",""]}]
2025-06-11T22:03:27.251Z [DEBUG] JSON file written: output/analysis/column_analysis_2025-06-11T22-03-27-250Z.json
2025-06-11T22:03:27.251Z [INFO] Analysis result saved to: output/analysis/column_analysis_2025-06-11T22-03-27-250Z.json
2025-06-11T22:03:27.251Z [DEBUG] JSON file written: output/analysis/latest.json
2025-06-11T22:03:27.251Z [DEBUG] Latest analysis result saved
2025-06-11T22:03:27.251Z [INFO] SUCCESS: Column analysis completed. Identified 21 columns with 100.0% confidence
2025-06-11T22:03:27.252Z [INFO] PDF service cleanup completed
2025-06-11T22:04:19.556Z [INFO] Initializing Extract Utility
2025-06-11T22:04:19.558Z [DEBUG] Environment variables loaded
2025-06-11T22:04:19.558Z [INFO] Configuration validation passed
2025-06-11T22:04:19.558Z [INFO] Initializing Analyze Utility
2025-06-11T22:04:19.558Z [INFO] Configuration validation passed
2025-06-11T22:04:19.617Z [INFO] PDF has 258 pages
2025-06-11T22:04:19.617Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-11T22:04:19.672Z [INFO] PDF has 258 pages
2025-06-11T22:04:19.674Z [INFO] SUCCESS: Extract Utility initialized successfully
2025-06-11T22:04:19.674Z [INFO] Starting data extraction process
2025-06-11T22:04:19.675Z [INFO] Using analysis with 21 columns
2025-06-11T22:04:19.675Z [INFO] PROGRESS: Processing batch 1 (10/258)
2025-06-11T22:04:19.675Z [INFO] PROGRESS: Converting page 1 to image
2025-06-11T22:04:25.854Z [DEBUG] Page 1 converted using ImageMagick
2025-06-11T22:04:25.924Z [DEBUG] Page 1 converted | Data: {"pageNumber":1,"imagePath":"temp/page_1.png","width":2480,"height":3508,"fileSize":5978314}
2025-06-11T22:04:25.924Z [INFO] PROGRESS: Extracting data from page 1
2025-06-11T22:04:26.412Z [ERROR] Failed to extract data from page 1 | Data: {}
2025-06-11T22:04:26.413Z [ERROR] Failed to process page 1 | Data: {}
2025-06-11T22:04:26.413Z [INFO] PROGRESS: Converting page 2 to image
2025-06-11T22:04:32.455Z [DEBUG] Page 2 converted using ImageMagick
2025-06-11T22:04:32.456Z [DEBUG] Page 2 converted | Data: {"pageNumber":2,"imagePath":"temp/page_2.png","width":2480,"height":3508,"fileSize":6071680}
2025-06-11T22:04:32.456Z [INFO] PROGRESS: Extracting data from page 2
2025-06-11T22:05:47.416Z [INFO] SUCCESS: Extracted 6 listings from page 2
2025-06-11T22:05:47.417Z [DEBUG] Page 2 processed: 6 entries in 81004ms
2025-06-11T22:05:47.417Z [INFO] PROGRESS: Converting page 3 to image
2025-06-11T22:05:54.447Z [DEBUG] Page 3 converted using ImageMagick
2025-06-11T22:05:54.448Z [DEBUG] Page 3 converted | Data: {"pageNumber":3,"imagePath":"temp/page_3.png","width":2480,"height":3508,"fileSize":6090309}
2025-06-11T22:05:54.448Z [INFO] PROGRESS: Extracting data from page 3
2025-06-11T22:05:54.829Z [ERROR] Failed to extract data from page 3 | Data: {}
2025-06-11T22:05:54.829Z [ERROR] Failed to process page 3 | Data: {}
2025-06-11T22:05:54.829Z [INFO] PROGRESS: Converting page 4 to image
2025-06-11T22:06:00.592Z [DEBUG] Page 4 converted using ImageMagick
2025-06-11T22:06:00.593Z [DEBUG] Page 4 converted | Data: {"pageNumber":4,"imagePath":"temp/page_4.png","width":2480,"height":3508,"fileSize":5766146}
2025-06-11T22:06:00.593Z [INFO] PROGRESS: Extracting data from page 4
2025-06-11T22:06:13.823Z [INFO] Initializing Extract Utility
2025-06-11T22:06:13.825Z [DEBUG] Environment variables loaded
2025-06-11T22:06:13.825Z [INFO] Configuration validation passed
2025-06-11T22:06:13.825Z [INFO] Initializing Analyze Utility
2025-06-11T22:06:13.825Z [INFO] Configuration validation passed
2025-06-11T22:06:13.884Z [INFO] PDF has 258 pages
2025-06-11T22:06:13.885Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-11T22:06:13.942Z [INFO] PDF has 258 pages
2025-06-11T22:06:13.942Z [INFO] SUCCESS: Extract Utility initialized successfully
2025-06-11T22:06:13.943Z [INFO] Starting data extraction process
2025-06-11T22:06:13.943Z [INFO] Using analysis with 21 columns
2025-06-11T22:06:13.944Z [INFO] PROGRESS: Processing batch 1 (10/258)
2025-06-11T22:06:13.944Z [INFO] PROGRESS: Converting page 1 to image
2025-06-11T22:06:20.098Z [DEBUG] Page 1 converted using ImageMagick
2025-06-11T22:06:20.133Z [DEBUG] Page 1 converted | Data: {"pageNumber":1,"imagePath":"temp/page_1.png","width":2480,"height":3508,"fileSize":5978314}
2025-06-11T22:06:20.134Z [INFO] PROGRESS: Extracting data from page 1
2025-06-11T22:06:20.640Z [ERROR] Failed to extract data from page 1 | Data: {}
2025-06-11T22:06:20.640Z [ERROR] Failed to process page 1 | Data: {}
2025-06-11T22:06:20.640Z [INFO] PROGRESS: Converting page 2 to image
2025-06-11T22:14:46.698Z [INFO] Initializing Convert Utility
2025-06-11T22:14:46.700Z [DEBUG] Environment variables loaded
2025-06-11T22:14:46.700Z [INFO] Configuration validation passed
2025-06-11T22:14:46.753Z [INFO] PDF has 258 pages
2025-06-11T22:14:46.753Z [INFO] SUCCESS: Convert Utility initialized successfully
2025-06-11T22:14:46.754Z [INFO] Starting PDF to PNG conversion process
2025-06-11T22:14:46.754Z [INFO] Converting pages 1 to 3 (3 pages total)
2025-06-11T22:14:46.754Z [INFO] PROGRESS: Processing batch 1/2 (2/3)
2025-06-11T22:14:46.754Z [DEBUG] Page 1 PNG already exists, skipping conversion (5.70MB)
2025-06-11T22:14:46.805Z [DEBUG] Using existing page 1 PNG | Data: {"pageNumber":1,"imagePath":"temp/page_1.png","width":2480,"height":3508,"fileSize":5978314}
2025-06-11T22:14:46.805Z [DEBUG] Page 2 PNG already exists, skipping conversion (5.79MB)
2025-06-11T22:14:46.806Z [DEBUG] Using existing page 2 PNG | Data: {"pageNumber":2,"imagePath":"temp/page_2.png","width":2480,"height":3508,"fileSize":6071680}
2025-06-11T22:14:47.308Z [INFO] PROGRESS: Processing batch 2/2 (3/3)
2025-06-11T22:14:47.309Z [DEBUG] Page 3 PNG already exists, skipping conversion (5.81MB)
2025-06-11T22:14:47.310Z [DEBUG] Using existing page 3 PNG | Data: {"pageNumber":3,"imagePath":"temp/page_3.png","width":2480,"height":3508,"fileSize":6090309}
2025-06-11T22:14:47.313Z [DEBUG] JSON file written: output/converted/convert_result_2025-06-11T22-14-47-311Z.json
2025-06-11T22:14:47.313Z [DEBUG] Conversion result saved: output/converted/convert_result_2025-06-11T22-14-47-311Z.json
2025-06-11T22:14:47.314Z [INFO] SUCCESS: PDF conversion completed in 0.6s
2025-06-11T22:14:47.314Z [INFO] Convert process finished
2025-06-11T22:16:00.668Z [INFO] Initializing Convert Utility
2025-06-11T22:16:00.669Z [DEBUG] Environment variables loaded
2025-06-11T22:16:00.669Z [INFO] Configuration validation passed
2025-06-11T22:16:00.724Z [INFO] PDF has 258 pages
2025-06-11T22:16:00.724Z [INFO] SUCCESS: Convert Utility initialized successfully
2025-06-11T22:16:00.724Z [INFO] Starting PDF to PNG conversion process
2025-06-11T22:16:00.724Z [INFO] Converting pages 1 to 258 (258 pages total)
2025-06-11T22:16:00.725Z [INFO] PROGRESS: Processing batch 1/26 (10/258)
2025-06-11T22:16:00.725Z [DEBUG] Page 1 PNG already exists, skipping conversion (5.70MB)
2025-06-11T22:16:00.742Z [DEBUG] Using existing page 1 PNG | Data: {"pageNumber":1,"imagePath":"temp/page_1.png","width":2480,"height":3508,"fileSize":5978314}
2025-06-11T22:16:00.743Z [DEBUG] Page 2 PNG already exists, skipping conversion (5.79MB)
2025-06-11T22:16:00.743Z [DEBUG] Using existing page 2 PNG | Data: {"pageNumber":2,"imagePath":"temp/page_2.png","width":2480,"height":3508,"fileSize":6071680}
2025-06-11T22:16:00.743Z [DEBUG] Page 3 PNG already exists, skipping conversion (5.81MB)
2025-06-11T22:16:00.744Z [DEBUG] Using existing page 3 PNG | Data: {"pageNumber":3,"imagePath":"temp/page_3.png","width":2480,"height":3508,"fileSize":6090309}
2025-06-11T22:16:00.744Z [DEBUG] Page 4 PNG already exists, skipping conversion (5.50MB)
2025-06-11T22:16:00.744Z [DEBUG] Using existing page 4 PNG | Data: {"pageNumber":4,"imagePath":"temp/page_4.png","width":2480,"height":3508,"fileSize":5766146}
2025-06-11T22:16:00.744Z [INFO] PROGRESS: Converting page 5 to image
2025-06-11T22:16:02.591Z [DEBUG] Page 5 converted using ImageMagick
2025-06-11T22:16:02.592Z [DEBUG] Page 5 converted: 1240x1754px, 1.90MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":5,"imagePath":"temp/page_5.png","width":1240,"height":1754,"fileSize":1996665}
2025-06-11T22:16:02.592Z [DEBUG] Page 6 PNG already exists, skipping conversion (5.71MB)
2025-06-11T22:16:02.593Z [DEBUG] Using existing page 6 PNG | Data: {"pageNumber":6,"imagePath":"temp/page_6.png","width":2480,"height":3508,"fileSize":5991147}
2025-06-11T22:16:02.593Z [INFO] PROGRESS: Converting page 7 to image
2025-06-11T22:16:13.654Z [INFO] Initializing Convert Utility
2025-06-11T22:16:13.655Z [DEBUG] Environment variables loaded
2025-06-11T22:16:13.656Z [INFO] Configuration validation passed
2025-06-11T22:16:13.711Z [INFO] PDF has 258 pages
2025-06-11T22:16:13.711Z [INFO] SUCCESS: Convert Utility initialized successfully
2025-06-11T22:16:13.712Z [INFO] Starting PDF to PNG conversion process
2025-06-11T22:16:13.712Z [INFO] Converting pages 1 to 258 (258 pages total)
2025-06-11T22:16:13.712Z [INFO] PROGRESS: Processing batch 1/26 (10/258)
2025-06-11T22:16:13.713Z [INFO] PROGRESS: Converting page 1 to image
2025-06-11T22:16:15.393Z [DEBUG] Page 1 converted using ImageMagick
2025-06-11T22:16:15.412Z [DEBUG] Page 1 converted: 1240x1754px, 1.85MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":1,"imagePath":"temp/page_1.png","width":1240,"height":1754,"fileSize":1940307}
2025-06-11T22:16:15.412Z [INFO] PROGRESS: Converting page 2 to image
2025-06-11T22:16:16.970Z [DEBUG] Page 2 converted using ImageMagick
2025-06-11T22:16:16.970Z [DEBUG] Page 2 converted: 1240x1754px, 1.83MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":2,"imagePath":"temp/page_2.png","width":1240,"height":1754,"fileSize":1919058}
2025-06-11T22:16:16.971Z [INFO] PROGRESS: Converting page 3 to image
2025-06-11T22:16:18.615Z [DEBUG] Page 3 converted using ImageMagick
2025-06-11T22:16:18.616Z [DEBUG] Page 3 converted: 1240x1754px, 1.86MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":3,"imagePath":"temp/page_3.png","width":1240,"height":1754,"fileSize":1953227}
2025-06-11T22:16:18.616Z [INFO] PROGRESS: Converting page 4 to image
2025-06-11T22:16:20.189Z [DEBUG] Page 4 converted using ImageMagick
2025-06-11T22:16:20.189Z [DEBUG] Page 4 converted: 1240x1754px, 1.76MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":4,"imagePath":"temp/page_4.png","width":1240,"height":1754,"fileSize":1847861}
2025-06-11T22:16:20.190Z [INFO] PROGRESS: Converting page 5 to image
2025-06-11T22:16:21.849Z [DEBUG] Page 5 converted using ImageMagick
2025-06-11T22:16:21.850Z [DEBUG] Page 5 converted: 1240x1754px, 1.90MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":5,"imagePath":"temp/page_5.png","width":1240,"height":1754,"fileSize":1996665}
2025-06-11T22:16:21.850Z [INFO] PROGRESS: Converting page 6 to image
2025-06-11T22:16:23.461Z [DEBUG] Page 6 converted using ImageMagick
2025-06-11T22:16:23.462Z [DEBUG] Page 6 converted: 1240x1754px, 1.83MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":6,"imagePath":"temp/page_6.png","width":1240,"height":1754,"fileSize":1918376}
2025-06-11T22:16:23.462Z [INFO] PROGRESS: Converting page 7 to image
2025-06-11T22:16:24.974Z [DEBUG] Page 7 converted using ImageMagick
2025-06-11T22:16:24.975Z [DEBUG] Page 7 converted: 1240x1754px, 1.69MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":7,"imagePath":"temp/page_7.png","width":1240,"height":1754,"fileSize":1774797}
2025-06-11T22:16:24.975Z [INFO] PROGRESS: Converting page 8 to image
2025-06-11T22:16:26.498Z [DEBUG] Page 8 converted using ImageMagick
2025-06-11T22:16:26.499Z [DEBUG] Page 8 converted: 1240x1754px, 1.67MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":8,"imagePath":"temp/page_8.png","width":1240,"height":1754,"fileSize":1751587}
2025-06-11T22:16:26.500Z [INFO] PROGRESS: Converting page 9 to image
2025-06-11T22:16:28.114Z [DEBUG] Page 9 converted using ImageMagick
2025-06-11T22:16:28.115Z [DEBUG] Page 9 converted: 1240x1754px, 1.75MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":9,"imagePath":"temp/page_9.png","width":1240,"height":1754,"fileSize":1838513}
2025-06-11T22:16:28.115Z [INFO] PROGRESS: Converting page 10 to image
2025-06-11T22:16:29.722Z [DEBUG] Page 10 converted using ImageMagick
2025-06-11T22:16:29.722Z [DEBUG] Page 10 converted: 1240x1754px, 1.71MB (reduced resolution/quality for smaller file size) | Data: {"pageNumber":10,"imagePath":"temp/page_10.png","width":1240,"height":1754,"fileSize":1790965}
2025-06-11T22:16:30.225Z [INFO] PROGRESS: Processing batch 2/26 (20/258)
2025-06-11T22:16:30.227Z [INFO] PROGRESS: Converting page 11 to image
