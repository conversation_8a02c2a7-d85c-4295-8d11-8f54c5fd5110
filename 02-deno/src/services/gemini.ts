import { GoogleGenerativeAI, type GenerativeModel } from '@google/generative-ai';
import type { GeminiConfig, ColumnDefinition, BusinessListing } from '../types.ts';
import { logger } from '../utils/logger.ts';
import { encodeBase64 } from '@std/encoding/base64.ts';

export class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;

  constructor(config: GeminiConfig) {
    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: config.model,
      generationConfig: {
        temperature: config.temperature,
        maxOutputTokens: config.maxTokens,
      },
    });
  }

  async analyzeColumnsFromImages(imagePaths: string[]): Promise<ColumnDefinition[]> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await logger.info(`Analyzing ${imagePaths.length} images for column structure (attempt ${attempt}/${maxRetries})`);

        const prompt = `
You are an expert at analyzing business directory listings. I'm providing you with images of pages from a business directory PDF.

Please analyze these images and identify ALL possible data columns/fields that could be extracted from the business listings.

For each column you identify, provide:
1. A clear, descriptive name
2. A brief description of what the field contains
3. The data type (string, number, email, phone, url, address)
4. Whether it appears to be required/always present (true/false)
5. 2-3 example values if visible

I know there are at least these fields: business name, category, contact person, phone, email, company address, mailing address, social media accounts, brand/franchise info.

But please identify ANY other fields you can see, such as:
- Website URLs
- Fax numbers
- Years in business
- Certifications
- Service areas
- Hours of operation
- Specialties
- Awards
- Membership information
- etc.

Return your response as a JSON array of objects with this structure:
{
  "name": "field_name",
  "description": "Description of the field",
  "dataType": "string|number|email|phone|url|address",
  "required": true|false,
  "examples": ["example1", "example2"]
}

Be thorough and identify every possible field you can see in the listings.
`;

        const imageParts = await Promise.all(
          imagePaths.map(async (imagePath) => {
            const imageData = await Deno.readFile(imagePath);
            // Convert Uint8Array to base64 safely using Deno's standard library
            const base64String = encodeBase64(imageData);
            return {
              inlineData: {
                data: base64String,
                mimeType: 'image/jpeg',
              },
            };
          })
        );

        const result = await this.model.generateContent([prompt, ...imageParts]);
        const response = result.response;
        const text = response.text();

        // Extract JSON from the response
        const jsonMatch = text.match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in Gemini response');
        }

        const columns: ColumnDefinition[] = JSON.parse(jsonMatch[0]);

        await logger.success(`Identified ${columns.length} potential columns`);
        await logger.debug('Identified columns', columns);

        return columns;
      } catch (error) {
        lastError = error as Error;
        await logger.warn(`Attempt ${attempt} failed: ${(error as Error).message}`);

        if (attempt < maxRetries) {
          const delay = attempt * 2000; // Exponential backoff: 2s, 4s
          await logger.info(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    await logger.error('Failed to analyze columns from images after all retries', lastError);
    throw lastError || new Error('Failed to analyze columns after all retries');
  }

  async extractDataFromImage(
    imagePath: string,
    columns: ColumnDefinition[],
    pageNumber: number
  ): Promise<BusinessListing[]> {
    try {
      await logger.progress(`Extracting data from page ${pageNumber}`);

      const columnDescriptions = columns.map(col =>
        `- ${col.name} (${col.dataType}): ${col.description}`
      ).join('\n');

      const prompt = `
You are an expert at extracting structured data from business directory listings.

I'm providing you with an image of a page from a business directory. Please extract ALL business listings from this page and structure the data according to the specified columns.

COLUMNS TO EXTRACT:
${columnDescriptions}

INSTRUCTIONS:
1. Extract every business listing you can find on this page
2. For each listing, extract data for all available columns
3. If a field is not visible/available, use null
4. If a listing appears to continue on another page, set "spanMultiplePages" to true
5. If this page continues a listing from a previous page, note it
6. Assign each listing a sequential entry number starting from 1
7. Provide a confidence score (0-1) for each extraction

Return your response as a JSON array of business listings with this structure:
{
  "pageNumber": ${pageNumber},
  "entryNumber": 1,
  "data": {
    "column_name": "extracted_value_or_null"
  },
  "confidence": 0.95,
  "spanMultiplePages": false,
  "continuedFromPage": null,
  "continuesOnPage": null
}

Be thorough and extract every listing you can see. If text is partially cut off or unclear, do your best to extract what you can and adjust the confidence score accordingly.
`;

      const imageData = await Deno.readFile(imagePath);
      // Convert Uint8Array to base64 safely using Deno's standard library
      const base64String = encodeBase64(imageData);
      const imagePart = {
        inlineData: {
          data: base64String,
          mimeType: 'image/jpeg',
        },
      };

      const result = await this.model.generateContent([prompt, imagePart]);
      const response = result.response;
      const text = response.text();

      // Extract JSON from the response
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in Gemini response');
      }

      const listings: BusinessListing[] = JSON.parse(jsonMatch[0]);

      await logger.success(`Extracted ${listings.length} listings from page ${pageNumber}`);

      return listings;
    } catch (error) {
      await logger.error(`Failed to extract data from page ${pageNumber}`, error);
      throw error;
    }
  }

  async processMultipleImages(
    imagePaths: string[],
    columns: ColumnDefinition[]
  ): Promise<BusinessListing[]> {
    const allListings: BusinessListing[] = [];

    for (let i = 0; i < imagePaths.length; i++) {
      try {
        const imagePath = imagePaths[i];
        if (!imagePath) {
          await logger.warn(`Skipping undefined image path at index ${i}`);
          continue;
        }

        const pageNumber = i + 1; // Assuming sequential processing

        const listings = await this.extractDataFromImage(imagePath, columns, pageNumber);
        allListings.push(...listings);

        // Add a small delay to avoid rate limiting
        if (i < imagePaths.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        await logger.error(`Failed to process image ${i + 1}`, error);
        // Continue with other images
      }
    }

    return allListings;
  }

  validateAndCleanData(listings: BusinessListing[]): BusinessListing[] {
    // Basic data validation and cleaning
    return listings.map(listing => {
      // Clean phone numbers
      if (listing.data.phone && typeof listing.data.phone === 'string') {
        listing.data.phone = listing.data.phone.replace(/[^\d\-\(\)\+\s]/g, '');
      }

      // Clean email addresses
      if (listing.data.email && typeof listing.data.email === 'string') {
        listing.data.email = listing.data.email.toLowerCase().trim();
      }

      // Clean URLs
      if (listing.data.website && typeof listing.data.website === 'string') {
        let url = listing.data.website.trim();
        if (url && !url.startsWith('http')) {
          url = 'https://' + url;
        }
        listing.data.website = url;
      }

      return listing;
    });
  }
}
