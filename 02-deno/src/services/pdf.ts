import type { PDFPageInfo } from '../types.ts';
import { logger } from '../utils/logger.ts';
import { FileUtils } from '../utils/file.ts';
import { join } from '@std/path/mod.ts';

export class PDFService {
  private pdfPath: string;
  private tempDir: string;
  private totalPages: number | null = null;

  constructor(pdfPath: string, tempDir: string) {
    this.pdfPath = pdfPath;
    this.tempDir = tempDir;
  }

  async init(): Promise<void> {
    await FileUtils.ensureDirectoryExists(this.tempDir);
    await this.getTotalPages();
  }

  async getTotalPages(): Promise<number> {
    if (this.totalPages !== null) {
      return this.totalPages;
    }

    try {
      // Try multiple methods to get page count
      this.totalPages = await this.getPageCountFromPDF();
      await logger.info(`PDF has ${this.totalPages} pages`);
      return this.totalPages;
    } catch (error) {
      await logger.error('Failed to get total pages from PDF', error);
      throw error;
    }
  }

  private async getPageCountFromPDF(): Promise<number> {
    // Try multiple methods to get page count

    // Method 1: Try pdfinfo if available
    try {
      const command = new Deno.Command('pdfinfo', {
        args: [this.pdfPath],
        stdout: 'piped',
        stderr: 'piped',
      });

      const { code, stdout } = await command.output();

      if (code === 0) {
        const output = new TextDecoder().decode(stdout);
        const match = output.match(/Pages:\s+(\d+)/);
        if (match) {
          return parseInt((match[1]!), 10);
        }
      }
    } catch {
      // Continue to next method
    }

    // Method 2: Try mdls (macOS metadata)
    try {
      const command = new Deno.Command('mdls', {
        args: ['-name', 'kMDItemNumberOfPages', this.pdfPath],
        stdout: 'piped',
        stderr: 'piped',
      });

      const { code, stdout } = await command.output();

      if (code === 0) {
        const output = new TextDecoder().decode(stdout);
        const match = output.match(/kMDItemNumberOfPages\s*=\s*(\d+)/);
        if (match) {
          return parseInt((match[1]!), 10);
        }
      }
    } catch {
      // Continue to next method
    }

    // Method 3: Try reading PDF file directly (basic approach)
    try {
      const pdfData = await Deno.readFile(this.pdfPath);
      const pdfText = new TextDecoder().decode(pdfData);

      // Look for /Count entries which often indicate page count
      const countMatches = pdfText.match(/\/Count\s+(\d+)/g);
      if (countMatches && countMatches.length > 0) {
        const counts = countMatches.map(match => {
          const num = match.match(/\/Count\s+(\d+)/);
          return num ? parseInt(num[1]!, 10) : 0;
        });
        // Take the maximum count found
        const maxCount = Math.max(...counts);
        if (maxCount > 0 && maxCount < 10000) { // Reasonable upper bound
          return maxCount;
        }
      }
    } catch {
      // Continue to fallback
    }

    // Fallback: assume reasonable number of pages
    await logger.warn('Could not determine page count, assuming 10 pages');
    return 10; // More conservative default
  }

  async convertPageToImage(pageNumber: number): Promise<PDFPageInfo> {
    try {
      const imagePath = join(this.tempDir, `page_${pageNumber}.png`);

      // Check if PNG already exists
      if (await FileUtils.fileExists(imagePath)) {
        const stat = await Deno.stat(imagePath);
        const fileSizeMB = (stat.size / (1024 * 1024)).toFixed(2);

        await logger.debug(`Page ${pageNumber} PNG already exists, skipping conversion (${fileSizeMB}MB)`);
        console.log(`⏭️  Page ${pageNumber} PNG already exists, skipping conversion (${fileSizeMB}MB)`);

        // Get existing image info
        // Use sharp to get image dimensions
        const sharp = (await import('sharp')).default;
        const metadata = await sharp(imagePath).metadata();

        const pageInfo: PDFPageInfo = {
          pageNumber,
          imagePath,
          width: metadata.width || 0,
          height: metadata.height || 0,
          fileSize: stat.size,
        };

        await logger.debug(`Using existing page ${pageNumber} PNG`, pageInfo);
        return pageInfo;
      }

      await logger.progress(`Converting page ${pageNumber} to image`);

      // Try multiple conversion methods
      let conversionSuccess = false;

      // Method 1: Try using ImageMagick convert (if available)
      if (!conversionSuccess) {
        try {
          const command = new Deno.Command('convert', {
            args: [
              '-density', '150', // Reduced from 300 to 150 (50% reduction)
              '-quality', '85',  // Reduced from 100 to 85 for smaller file size
              `${this.pdfPath}[${pageNumber - 1}]`, // ImageMagick uses 0-based indexing
              imagePath
            ],
            stdout: 'piped',
            stderr: 'piped',
          });

          const { code } = await command.output();
          if (code === 0 && await FileUtils.fileExists(imagePath)) {
            conversionSuccess = true;
            await logger.debug(`Page ${pageNumber} converted using ImageMagick`);
          }
        } catch {
          // Continue to next method
        }
      }

      // Method 2: Try using GraphicsMagick (if available)
      if (!conversionSuccess) {
        try {
          const command = new Deno.Command('gm', {
            args: [
              'convert',
              '-density', '150', // Reduced from 300 to 150 (50% reduction)
              '-quality', '85',  // Reduced from 100 to 85 for smaller file size
              `${this.pdfPath}[${pageNumber - 1}]`,
              imagePath
            ],
            stdout: 'piped',
            stderr: 'piped',
          });

          const { code } = await command.output();
          if (code === 0 && await FileUtils.fileExists(imagePath)) {
            conversionSuccess = true;
            await logger.debug(`Page ${pageNumber} converted using GraphicsMagick`);
          }
        } catch {
          // Continue to next method
        }
      }

      // Method 3: Try using pdftoppm (if available)
      if (!conversionSuccess) {
        try {
          const command = new Deno.Command('pdftoppm', {
            args: [
              '-png',
              '-r', '150', // Reduced from 300 to 150 (50% reduction)
              '-f', pageNumber.toString(),
              '-l', pageNumber.toString(),
              this.pdfPath,
              join(this.tempDir, `page_${pageNumber}`)
            ],
            stdout: 'piped',
            stderr: 'piped',
          });

          const { code } = await command.output();
          // pdftoppm creates files with format: prefix-pagenumber.png
          const pdftoppmPath = join(this.tempDir, `page_${pageNumber}-${pageNumber.toString().padStart(6, '0')}.png`);

          if (code === 0 && await FileUtils.fileExists(pdftoppmPath)) {
            // Rename to our expected format
            await Deno.rename(pdftoppmPath, imagePath);
            conversionSuccess = true;
            await logger.debug(`Page ${pageNumber} converted using pdftoppm`);
          }
        } catch {
          // Continue to fallback
        }
      }

      if (!conversionSuccess) {
        throw new Error(`Failed to convert page ${pageNumber} - no suitable PDF conversion tool found. Please install ImageMagick, GraphicsMagick, or poppler-utils.`);
      }

      // Get image dimensions and file size
      const stat = await Deno.stat(imagePath);

      // Use sharp to get image dimensions
      const sharp = (await import('sharp')).default;
      const metadata = await sharp(imagePath).metadata();

      const pageInfo: PDFPageInfo = {
        pageNumber,
        imagePath,
        width: metadata.width || 0,
        height: metadata.height || 0,
        fileSize: stat.size,
      };

      // Log file size in a human-readable format
      const fileSizeMB = (stat.size / (1024 * 1024)).toFixed(2);
      await logger.debug(`Page ${pageNumber} converted: ${metadata.width}x${metadata.height}px, ${fileSizeMB}MB (reduced resolution/quality for smaller file size)`, pageInfo);

      return pageInfo;
    } catch (error) {
      await logger.error(`Failed to convert page ${pageNumber} to image`, error);
      throw error;
    }
  }

  async convertPagesToImages(pageNumbers: number[]): Promise<PDFPageInfo[]> {
    const results: PDFPageInfo[] = [];

    for (const pageNumber of pageNumbers) {
      try {
        const pageInfo = await this.convertPageToImage(pageNumber);
        results.push(pageInfo);
      } catch (error) {
        await logger.error(`Failed to convert page ${pageNumber}`, error);
        // Continue with other pages
      }
    }

    return results;
  }

  getRandomPageNumbers(count: number): number[] {
    if (!this.totalPages) {
      throw new Error('Total pages not determined. Call init() first.');
    }

    const pageNumbers: number[] = [];
    const maxPages = Math.min(count, this.totalPages);

    while (pageNumbers.length < maxPages) {
      const randomPage = Math.floor(Math.random() * this.totalPages) + 1;
      if (!pageNumbers.includes(randomPage)) {
        pageNumbers.push(randomPage);
      }
    }

    return pageNumbers.sort((a, b) => a - b);
  }

  getAllPageNumbers(): number[] {
    if (!this.totalPages) {
      throw new Error('Total pages not determined. Call init() first.');
    }

    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  async cleanup(): Promise<void> {
    try {
      await FileUtils.cleanupTempFiles(this.tempDir);
      await logger.info('PDF service cleanup completed');
    } catch (error) {
      await logger.warn('Failed to cleanup PDF service', error);
    }
  }
}
