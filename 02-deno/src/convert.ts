import { join } from '@std/path/mod.ts';
import { Config } from './config.ts';
import { PDFService } from './services/pdf.ts';
import { logger } from './utils/logger.ts';
import { FileUtils } from './utils/file.ts';
import type { PDFPageInfo } from './types.ts';

export interface ConvertResult {
  timestamp: string;
  totalPages: number;
  convertedPages: number;
  skippedPages: number;
  failedPages: number;
  totalFileSize: number;
  averageFileSize: number;
  processingTime: number;
  pageResults: PDFPageInfo[];
  errors: Array<{ pageNumber: number; error: string }>;
}

export class ConvertUtility {
  private config: Config;
  private pdfService: PDFService;

  constructor() {
    this.config = {} as Config;
    this.pdfService = {} as PDFService;
  }

  async init(): Promise<void> {
    await logger.init();
    await logger.info('Initializing Convert Utility');

    // Load configuration
    this.config = await Config.getInstance();
    await this.config.validateConfig();

    const processingConfig = this.config.getProcessingConfig();

    // Initialize PDF service
    this.pdfService = new PDFService(processingConfig.pdfPath, processingConfig.tempDir);
    await this.pdfService.init();

    await logger.success('Convert Utility initialized successfully');
  }

  async convert(options?: {
    batchSize?: number;
    startPage?: number;
    endPage?: number;
  }): Promise<ConvertResult> {
    try {
      const startTime = Date.now();
      await logger.info('Starting PDF to PNG conversion process');

      const processingConfig = this.config.getProcessingConfig();
      const totalPages = await this.pdfService.getTotalPages();

      // Determine page range
      const startPage = options?.startPage || 1;
      const endPage = options?.endPage || totalPages;
      const pageRange = Array.from(
        { length: endPage - startPage + 1 },
        (_, i) => startPage + i
      ).filter(page => page >= 1 && page <= totalPages);

      await logger.info(`Converting pages ${startPage} to ${endPage} (${pageRange.length} pages total)`);
      console.log(`📄 Converting pages ${startPage} to ${endPage} (${pageRange.length} pages total)`);

      // Process pages in batches
      const batchSize = options?.batchSize || processingConfig.maxPagesPerBatch;
      const pageResults: PDFPageInfo[] = [];
      const errors: Array<{ pageNumber: number; error: string }> = [];
      let convertedPages = 0;
      let skippedPages = 0;

      for (let i = 0; i < pageRange.length; i += batchSize) {
        const batch = pageRange.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(pageRange.length / batchSize);

        await logger.progress(`Processing batch ${batchNumber}/${totalBatches}`, i + batch.length, pageRange.length);
        console.log(`\n🔄 Processing batch ${batchNumber}/${totalBatches} (pages ${batch[0]} to ${batch[batch.length - 1]})`);

        const batchResults = await this.processBatch(batch);

        // Categorize results
        for (const result of batchResults) {
          if (result.error) {
            errors.push({ pageNumber: result.pageNumber, error: result.error });
          } else if (result.skipped) {
            skippedPages++;
            pageResults.push(result.pageInfo!);
          } else {
            convertedPages++;
            pageResults.push(result.pageInfo!);
          }
        }

        // Small delay between batches to avoid overwhelming the system
        if (i + batchSize < pageRange.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      const processingTime = Date.now() - startTime;
      const failedPages = errors.length;
      const totalFileSize = pageResults.reduce((sum, page) => sum + page.fileSize, 0);
      const averageFileSize = pageResults.length > 0 ? totalFileSize / pageResults.length : 0;

      const result: ConvertResult = {
        timestamp: new Date().toISOString(),
        totalPages: pageRange.length,
        convertedPages,
        skippedPages,
        failedPages,
        totalFileSize,
        averageFileSize,
        processingTime,
        pageResults,
        errors,
      };

      // Save conversion result
      await this.saveConvertResult(result);

      // Display summary
      await this.displaySummary(result);

      await logger.success(`PDF conversion completed in ${(processingTime / 1000).toFixed(1)}s`);
      return result;

    } catch (error) {
      await logger.error('PDF conversion failed', error);
      throw error;
    } finally {
      // Note: We don't cleanup temp files here since they're the output we want to keep
      await logger.info('Convert process finished');
    }
  }

  private async processBatch(pageNumbers: number[]): Promise<Array<{
    pageNumber: number;
    pageInfo?: PDFPageInfo;
    skipped?: boolean;
    error?: string;
  }>> {
    const results = [];

    for (const pageNumber of pageNumbers) {
      try {
        console.log(`  📄 Converting page ${pageNumber}...`);

        const pageInfo = await this.pdfService.convertPageToImage(pageNumber);

        const fileSizeMB = (pageInfo.fileSize / (1024 * 1024)).toFixed(2);
        console.log(`  ✅ Page ${pageNumber}: ${pageInfo.width}x${pageInfo.height}px, ${fileSizeMB}MB`);

        results.push({
          pageNumber,
          pageInfo,
          skipped: false,
        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`  ❌ Page ${pageNumber}: ${errorMessage}`);

        results.push({
          pageNumber,
          error: errorMessage,
        });
      }
    }

    return results;
  }

  private async saveConvertResult(result: ConvertResult): Promise<void> {
    const processingConfig = this.config.getProcessingConfig();
    const outputDir = join(processingConfig.outputDir, 'converted');
    await FileUtils.ensureDirectoryExists(outputDir);

    const filename = FileUtils.generateTimestampedFilename('convert_result', 'json');
    const filePath = join(outputDir, filename);

    await FileUtils.writeJsonFile(filePath, result);
    await logger.debug(`Conversion result saved: ${filePath}`);
  }

  private async displaySummary(result: ConvertResult): Promise<void> {
    const totalSizeMB = (result.totalFileSize / (1024 * 1024)).toFixed(2);
    const avgSizeMB = (result.averageFileSize / (1024 * 1024)).toFixed(2);
    const processingTimeSeconds = (result.processingTime / 1000).toFixed(1);

    console.log('\n📊 Conversion Summary:');
    console.log(`📄 Total pages processed: ${result.totalPages}`);
    console.log(`✅ Successfully converted: ${result.convertedPages}`);
    console.log(`⏭️  Skipped (already existed): ${result.skippedPages}`);
    console.log(`❌ Failed conversions: ${result.failedPages}`);
    console.log(`💾 Total file size: ${totalSizeMB}MB`);
    console.log(`📏 Average file size: ${avgSizeMB}MB`);
    console.log(`⏱️  Processing time: ${processingTimeSeconds}s`);

    if (result.errors.length > 0) {
      console.log('\n❌ Failed Pages:');
      for (const error of result.errors) {
        console.log(`  Page ${error.pageNumber}: ${error.error}`);
      }
    }

    const processingConfig = this.config.getProcessingConfig();
    console.log(`\n📁 PNG files saved to: ${processingConfig.tempDir}`);
  }

  async getConversionStatistics(): Promise<{
    totalConversions: number;
    latestConversion?: ConvertResult;
    totalFilesSize: number;
  }> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const convertedDir = join(processingConfig.outputDir, 'converted');

      if (!await FileUtils.fileExists(convertedDir)) {
        return {
          totalConversions: 0,
          totalFilesSize: 0,
        };
      }

      const files = await FileUtils.listFiles(convertedDir, '.json');
      let latestConversion: ConvertResult | undefined;
      let totalFilesSize = 0;

      for (const file of files) {
        try {
          const result = await FileUtils.readJsonFile<ConvertResult>(file);
          totalFilesSize += result.totalFileSize;

          if (!latestConversion || new Date(result.timestamp) > new Date(latestConversion.timestamp)) {
            latestConversion = result;
          }
        } catch (error) {
          await logger.warn(`Failed to load conversion result: ${file}`, error);
        }
      }

      return {
        totalConversions: files.length,
        ...(latestConversion && { latestConversion }),
        totalFilesSize,
      };
    } catch (error) {
      await logger.error('Failed to get conversion statistics', error);
      return {
        totalConversions: 0,
        totalFilesSize: 0,
      };
    }
  }
}

// CLI function for direct usage
export async function runConversion(options?: {
  batchSize?: number;
  startPage?: number;
  endPage?: number;
}): Promise<void> {
  const converter = new ConvertUtility();

  try {
    await converter.init();
    const result = await converter.convert(options);

    console.log('\n🎉 Conversion completed successfully!');

    if (result.convertedPages > 0) {
      console.log('💡 Next steps:');
      console.log('   • Run "deno task analyze" to analyze the converted pages');
      console.log('   • Run "deno task extract" to extract data from all pages');
    }

  } catch (error) {
    console.error('❌ Conversion failed:', error);
    Deno.exit(1);
  }
}
