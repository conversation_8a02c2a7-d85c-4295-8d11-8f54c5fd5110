import { join } from '@std/path/mod.ts';
import { Config } from './config.ts';
import { PDFService } from './services/pdf.ts';
import { GeminiService } from './services/gemini.ts';
import { logger } from './utils/logger.ts';
import { FileUtils } from './utils/file.ts';
import type { AnalysisResult, ColumnDefinition } from './types.ts';

export class AnalyzeUtility {
  private config: Config;
  private pdfService: PDFService;
  private geminiService: GeminiService;

  constructor() {
    this.config = {} as Config; // Will be initialized in init()
    this.pdfService = {} as PDFService; // Will be initialized in init()
    this.geminiService = {} as GeminiService; // Will be initialized in init()
  }

  async init(): Promise<void> {
    await logger.init();
    await logger.info('Initializing Analyze Utility');

    // Load configuration
    this.config = await Config.getInstance();
    await this.config.validateConfig();

    const geminiConfig = this.config.getGeminiConfig();
    const processingConfig = this.config.getProcessingConfig();

    // Initialize services
    this.pdfService = new PDFService(processingConfig.pdfPath, processingConfig.tempDir);
    this.geminiService = new GeminiService(geminiConfig);

    await this.pdfService.init();

    await logger.success('Analyze Utility initialized successfully');
  }

  async analyze(): Promise<AnalysisResult> {
    try {
      await logger.info('Starting column analysis process');

      const processingConfig = this.config.getProcessingConfig();
      const totalPages = await this.pdfService.getTotalPages();

      // Get random sample pages
      const samplePages = this.pdfService.getRandomPageNumbers(processingConfig.samplePages);
      await logger.info(`Analyzing ${samplePages.length} random pages: ${samplePages.join(', ')}`);

      // Convert sample pages to images
      await logger.progress('Converting sample pages to images');
      const pageInfos = await this.pdfService.convertPagesToImages(samplePages);

      if (pageInfos.length === 0) {
        throw new Error('No pages were successfully converted to images');
      }

      // Extract image paths
      const imagePaths = pageInfos.map(info => info.imagePath);

      // Analyze columns using Gemini
      await logger.progress('Analyzing images with Gemini AI');
      const columns = await this.geminiService.analyzeColumnsFromImages(imagePaths);

      // Calculate confidence based on consistency across pages
      const confidence = this.calculateAnalysisConfidence(columns, pageInfos.length);

      // Create analysis result
      const analysisResult: AnalysisResult = {
        timestamp: new Date().toISOString(),
        pagesAnalyzed: samplePages,
        totalPages,
        columns,
        confidence,
        notes: `Analysis based on ${pageInfos.length} random pages from ${totalPages} total pages`,
      };

      // Save analysis result
      await this.saveAnalysisResult(analysisResult);

      await logger.success(`Column analysis completed. Identified ${columns.length} columns with ${(confidence * 100).toFixed(1)}% confidence`);

      return analysisResult;
    } catch (error) {
      await logger.error('Column analysis failed', error);
      throw error;
    } finally {
      // Cleanup temporary files
      await this.pdfService.cleanup();
    }
  }

  private calculateAnalysisConfidence(columns: ColumnDefinition[], pagesAnalyzed: number): number {
    // Base confidence on number of columns found and pages analyzed
    let confidence = 0.7; // Base confidence

    // Increase confidence based on number of columns found
    if (columns.length >= 8) confidence += 0.15;
    else if (columns.length >= 5) confidence += 0.1;
    else if (columns.length >= 3) confidence += 0.05;

    // Increase confidence based on number of pages analyzed
    if (pagesAnalyzed >= 5) confidence += 0.1;
    else if (pagesAnalyzed >= 3) confidence += 0.05;

    // Check for expected core columns
    const coreColumns = ['business_name', 'name', 'company', 'phone', 'email', 'address'];
    const foundCoreColumns = columns.filter(col =>
      coreColumns.some(core => col.name.toLowerCase().includes(core))
    );

    if (foundCoreColumns.length >= 4) confidence += 0.1;
    else if (foundCoreColumns.length >= 2) confidence += 0.05;

    return Math.min(confidence, 1.0);
  }

  private async saveAnalysisResult(result: AnalysisResult): Promise<void> {
    const processingConfig = this.config.getProcessingConfig();
    const analysisDir = join(processingConfig.outputDir, 'analysis');
    await FileUtils.ensureDirectoryExists(analysisDir);

    const filename = FileUtils.generateTimestampedFilename('column_analysis', 'json');
    const filePath = join(analysisDir, filename);

    await FileUtils.writeJsonFile(filePath, result);
    await logger.info(`Analysis result saved to: ${filePath}`);

    // Also save a latest.json for easy access
    const latestPath = join(analysisDir, 'latest.json');
    await FileUtils.writeJsonFile(latestPath, result);
    await logger.debug('Latest analysis result saved');
  }

  async getLatestAnalysis(): Promise<AnalysisResult | null> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const latestPath = join(processingConfig.outputDir, 'analysis', 'latest.json');

      if (await FileUtils.fileExists(latestPath)) {
        return await FileUtils.readJsonFile<AnalysisResult>(latestPath);
      }

      return null;
    } catch (error) {
      await logger.warn('Failed to load latest analysis', error);
      return null;
    }
  }

  async listAnalysisResults(): Promise<string[]> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const analysisDir = join(processingConfig.outputDir, 'analysis');

      if (await FileUtils.fileExists(analysisDir)) {
        return await FileUtils.listFiles(analysisDir, '.json');
      }

      return [];
    } catch (error) {
      await logger.warn('Failed to list analysis results', error);
      return [];
    }
  }
}

// CLI function for direct usage
export async function runAnalysis(): Promise<void> {
  const analyzer = new AnalyzeUtility();

  try {
    await analyzer.init();
    const result = await analyzer.analyze();

    console.log('\n📊 Analysis Results:');
    console.log(`📄 Pages analyzed: ${result.pagesAnalyzed.join(', ')}`);
    console.log(`📋 Columns identified: ${result.columns.length}`);
    console.log(`🎯 Confidence: ${(result.confidence * 100).toFixed(1)}%`);
    console.log('\n📝 Identified Columns:');

    result.columns.forEach((col, index) => {
      console.log(`${index + 1}. ${col.name} (${col.dataType}) - ${col.description}`);
      if (col.examples && col.examples.length > 0) {
        console.log(`   Examples: ${col.examples.join(', ')}`);
      }
    });

  } catch (error) {
    console.error('❌ Analysis failed:', error);
    Deno.exit(1);
  }
}
