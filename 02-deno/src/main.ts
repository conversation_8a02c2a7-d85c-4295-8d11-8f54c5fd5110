#!/usr/bin/env -S deno run --allow-all

import { Command } from 'commander';
import { runAnalysis } from './analyze.ts';
import { runExtraction } from './extract.ts';
import { runCombine } from './combine.ts';
import { runConversion } from './convert.ts';
import { logger } from './utils/logger.ts';

const program = new Command();

program
  .name('guide-ocr')
  .description('TypeScript/Deno application for OCR processing of PDF business listings with Google Gemini AI')
  .version('1.0.0');

program
  .command('analyze')
  .description('Analyze random pages from PDF to identify potential data columns')
  .option('-p, --pages <number>', 'Number of sample pages to analyze', '5')
  .action(async (options) => {
    try {
      console.log('🔍 Starting column analysis...\n');

      // Set sample pages if provided
      if (options.pages) {
        Deno.env.set('SAMPLE_PAGES', options.pages);
      }

      await runAnalysis();

      console.log('\n✅ Analysis completed successfully!');
      console.log('💡 Next step: Run "deno task extract" to extract data from all pages');

    } catch (error) {
      console.error('\n❌ Analysis failed:', error);
      Deno.exit(1);
    }
  });

program
  .command('extract')
  .description('Extract data from all PDF pages using identified columns')
  .option('-b, --batch-size <number>', 'Number of pages to process per batch', '10')
  .action(async (options) => {
    try {
      console.log('📄 Starting data extraction...\n');

      // Set batch size if provided
      if (options.batchSize) {
        Deno.env.set('MAX_PAGES_PER_BATCH', options.batchSize);
      }

      await runExtraction();

      console.log('\n✅ Extraction completed successfully!');
      console.log('💡 Next step: Run "deno task combine" to combine data into Excel format');

    } catch (error) {
      console.error('\n❌ Extraction failed:', error);
      Deno.exit(1);
    }
  });

program
  .command('convert')
  .description('Convert all PDF pages to PNG images')
  .option('-b, --batch-size <number>', 'Number of pages to process per batch', '10')
  .option('-s, --start-page <number>', 'Starting page number', '1')
  .option('-e, --end-page <number>', 'Ending page number (default: last page)')
  .action(async (options) => {
    try {
      console.log('🖼️  Starting PDF to PNG conversion...\n');

      const convertOptions: {
        batchSize?: number;
        startPage?: number;
        endPage?: number;
      } = {};

      if (options.batchSize) {
        convertOptions.batchSize = parseInt(options.batchSize, 10);
      }
      if (options.startPage) {
        convertOptions.startPage = parseInt(options.startPage, 10);
      }
      if (options.endPage) {
        convertOptions.endPage = parseInt(options.endPage, 10);
      }

      await runConversion(convertOptions);

      console.log('\n✅ Conversion completed successfully!');
      console.log('💡 Next steps: Run "deno task analyze" or "deno task extract" to process the images');

    } catch (error) {
      console.error('\n❌ Conversion failed:', error);
      Deno.exit(1);
    }
  });

program
  .command('combine')
  .description('Combine extracted data and export to Excel')
  .option('-f, --filename <string>', 'Output Excel filename', 'business_listings.xlsx')
  .action(async (options) => {
    try {
      console.log('📊 Starting data combination and Excel export...\n');

      // Set filename if provided
      if (options.filename) {
        Deno.env.set('EXCEL_FILENAME', options.filename);
      }

      await runCombine();

      console.log('\n✅ Combination and export completed successfully!');

    } catch (error) {
      console.error('\n❌ Combination failed:', error);
      Deno.exit(1);
    }
  });

program
  .command('full')
  .description('Run complete pipeline: analyze → extract → combine')
  .option('-p, --pages <number>', 'Number of sample pages to analyze', '5')
  .option('-b, --batch-size <number>', 'Number of pages to process per batch', '10')
  .option('-f, --filename <string>', 'Output Excel filename', 'business_listings.xlsx')
  .action(async (options) => {
    try {
      console.log('🚀 Starting full OCR pipeline...\n');

      // Set options
      if (options.pages) Deno.env.set('SAMPLE_PAGES', options.pages);
      if (options.batchSize) Deno.env.set('MAX_PAGES_PER_BATCH', options.batchSize);
      if (options.filename) Deno.env.set('EXCEL_FILENAME', options.filename);

      // Step 1: Analyze
      console.log('📋 Step 1/3: Analyzing columns...');
      await runAnalysis();
      console.log('✅ Analysis completed\n');

      // Step 2: Extract
      console.log('📄 Step 2/3: Extracting data...');
      await runExtraction();
      console.log('✅ Extraction completed\n');

      // Step 3: Combine
      console.log('📊 Step 3/3: Combining and exporting...');
      await runCombine();
      console.log('✅ Export completed\n');

      console.log('🎉 Full pipeline completed successfully!');

    } catch (error) {
      console.error('\n❌ Pipeline failed:', error);
      Deno.exit(1);
    }
  });

program
  .command('setup')
  .description('Setup and validate configuration')
  .action(async () => {
    try {
      console.log('⚙️  Setting up Guide OCR...\n');

      // Import and validate config
      const { Config } = await import('./config.ts');
      const config = await Config.getInstance();

      console.log('📋 Validating configuration...');
      await config.validateConfig();

      console.log('✅ Configuration is valid!');

      // Check if PDF exists
      const processingConfig = config.getProcessingConfig();
      console.log(`📄 PDF file: ${processingConfig.pdfPath}`);

      try {
        const stat = await Deno.stat(processingConfig.pdfPath);
        console.log(`📏 File size: ${(stat.size / 1024 / 1024).toFixed(2)} MB`);
      } catch {
        console.log('❌ PDF file not found!');
        Deno.exit(1);
      }

      // Test Gemini API
      console.log('🤖 Testing Gemini API connection...');
      const { GeminiService } = await import('./services/gemini.ts');
      const geminiConfig = config.getGeminiConfig();
      new GeminiService(geminiConfig); // Just test instantiation

      // This would require a simple test call to Gemini
      console.log('✅ Gemini API configuration looks good!');

      console.log('\n🎉 Setup completed successfully!');
      console.log('💡 Next step: Run "deno task analyze" to start the OCR process');

    } catch (error) {
      console.error('\n❌ Setup failed:', error);
      console.log('\n💡 Make sure to:');
      console.log('1. Copy .env.example to .env');
      console.log('2. Set your GEMINI_API_KEY in .env');
      console.log('3. Ensure the PDF file exists at the specified path');
      Deno.exit(1);
    }
  });

program
  .command('clean')
  .description('Clean temporary files and output directories')
  .option('--all', 'Clean all output files including results')
  .action(async (options) => {
    try {
      console.log('🧹 Cleaning up files...\n');

      const { Config } = await import('./config.ts');
      const config = await Config.getInstance();
      const processingConfig = config.getProcessingConfig();

      // Clean temp directory
      try {
        await Deno.remove(processingConfig.tempDir, { recursive: true });
        console.log('✅ Cleaned temp directory');
      } catch {
        console.log('ℹ️  Temp directory already clean');
      }

      if (options.all) {
        // Clean output directories
        try {
          await Deno.remove(processingConfig.outputDir, { recursive: true });
          console.log('✅ Cleaned output directory');
        } catch {
          console.log('ℹ️  Output directory already clean');
        }

        // Clean logs
        try {
          await Deno.remove(processingConfig.logsDir, { recursive: true });
          console.log('✅ Cleaned logs directory');
        } catch {
          console.log('ℹ️  Logs directory already clean');
        }
      }

      console.log('\n✅ Cleanup completed!');

    } catch (error) {
      console.error('\n❌ Cleanup failed:', error);
      Deno.exit(1);
    }
  });

// Handle unhandled errors
globalThis.addEventListener('unhandledrejection', async (event) => {
  await logger.error('Unhandled promise rejection', event.reason);
  console.error('❌ Unhandled error:', event.reason);
  Deno.exit(1);
});

// Parse command line arguments
if (import.meta.main) {
  // Commander.js expects argv format: [node, script, ...args]
  // Deno.args only contains the actual arguments, so we need to prepend the executable and script name
  const argv = ['deno', 'src/main.ts', ...Deno.args];
  program.parse(argv);
}
