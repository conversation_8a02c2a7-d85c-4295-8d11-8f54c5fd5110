import { join } from '@std/path/mod.ts';
import { Config } from './config.ts';
import { PDFService } from './services/pdf.ts';
import { GeminiService } from './services/gemini.ts';
import { AnalyzeUtility } from './analyze.ts';
import { logger } from './utils/logger.ts';
import { FileUtils } from './utils/file.ts';
import type { AnalysisResult, ExtractionResult, BusinessListing } from './types.ts';

export class ExtractUtility {
  private config: Config;
  private pdfService: PDFService;
  private geminiService: GeminiService;
  private analyzeUtility: AnalyzeUtility;

  constructor() {
    this.config = {} as Config;
    this.pdfService = {} as PDFService;
    this.geminiService = {} as GeminiService;
    this.analyzeUtility = new AnalyzeUtility();
  }

  async init(): Promise<void> {
    await logger.init();
    await logger.info('Initializing Extract Utility');

    // Load configuration
    this.config = await Config.getInstance();
    await this.config.validateConfig();

    const geminiConfig = this.config.getGeminiConfig();
    const processingConfig = this.config.getProcessingConfig();

    // Initialize services
    this.pdfService = new PDFService(processingConfig.pdfPath, processingConfig.tempDir);
    this.geminiService = new GeminiService(geminiConfig);
    await this.analyzeUtility.init();

    await this.pdfService.init();

    await logger.success('Extract Utility initialized successfully');
  }

  async extract(analysisResult?: AnalysisResult): Promise<ExtractionResult[]> {
    try {
      await logger.info('Starting data extraction process');

      // Get or load analysis result
      let analysis = analysisResult;
      if (!analysis) {
        const latestAnalysis = await this.analyzeUtility.getLatestAnalysis();
        if (!latestAnalysis) {
          throw new Error('No analysis result found. Please run analysis first.');
        }
        analysis = latestAnalysis;
      }

      await logger.info(`Using analysis with ${analysis.columns.length} columns`);

      const processingConfig = this.config.getProcessingConfig();
      await this.pdfService.getTotalPages(); // Ensure PDF is initialized
      const allPages = this.pdfService.getAllPageNumbers();

      // Process pages in batches
      const batchSize = processingConfig.maxPagesPerBatch;
      const results: ExtractionResult[] = [];

      for (let i = 0; i < allPages.length; i += batchSize) {
        const batch = allPages.slice(i, i + batchSize);
        await logger.progress(`Processing batch ${Math.floor(i / batchSize) + 1}`, i + batch.length, allPages.length);

        const batchResults = await this.processBatch(batch, analysis);
        results.push(...batchResults);

        // Save intermediate results
        await this.saveBatchResults(batchResults);

        // Small delay between batches to avoid rate limiting
        if (i + batchSize < allPages.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      await logger.success(`Data extraction completed. Processed ${results.length} pages`);
      return results;

    } catch (error) {
      await logger.error('Data extraction failed', error);
      throw error;
    } finally {
      await this.pdfService.cleanup();
    }
  }

  private async processBatch(
    pageNumbers: number[],
    analysis: AnalysisResult
  ): Promise<ExtractionResult[]> {
    const results: ExtractionResult[] = [];

    for (const pageNumber of pageNumbers) {
      const result = await this.processPageWithRetry(pageNumber, analysis);
      results.push(result);
    }

    return results;
  }

  private async processPageWithRetry(
    pageNumber: number,
    analysis: AnalysisResult,
    maxRetries: number = 3
  ): Promise<ExtractionResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const startTime = Date.now();

        if (attempt > 1) {
          await logger.info(`🔄 Retry attempt ${attempt}/${maxRetries} for page ${pageNumber}`);
          console.log(`🔄 Retry attempt ${attempt}/${maxRetries} for page ${pageNumber}`);
        }

        // Convert page to image
        const pageInfo = await this.pdfService.convertPageToImage(pageNumber);

        // Extract data from the page
        const listings = await this.geminiService.extractDataFromImage(
          pageInfo.imagePath,
          analysis.columns,
          pageNumber
        );

        // Clean and validate the data
        const cleanedListings = this.geminiService.validateAndCleanData(listings);

        // Handle multi-page entries
        const processedListings = this.handleMultiPageEntries(cleanedListings, pageNumber);

        const processingTime = Date.now() - startTime;

        const result: ExtractionResult = {
          timestamp: new Date().toISOString(),
          pageNumber,
          totalEntries: processedListings.length,
          listings: processedListings,
          processingTime,
        };

        await logger.debug(`Page ${pageNumber} processed: ${processedListings.length} entries in ${processingTime}ms`);

        if (attempt > 1) {
          await logger.success(`✅ Page ${pageNumber} succeeded on attempt ${attempt}`);
          console.log(`✅ Page ${pageNumber} succeeded on attempt ${attempt}`);
        }

        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        const errorMessage = `❌ Attempt ${attempt}/${maxRetries} failed for page ${pageNumber}: ${lastError.message}`;
        await logger.error(errorMessage, lastError);
        console.error(errorMessage);

        if (lastError.stack) {
          console.error(`Stack trace: ${lastError.stack}`);
        }

        // If this is not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          const waitTime = attempt * 1000; // Progressive backoff: 1s, 2s, 3s
          await logger.info(`⏳ Waiting ${waitTime}ms before retry...`);
          console.log(`⏳ Waiting ${waitTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // All retries failed, create error result
    const finalErrorMessage = `Failed to process page ${pageNumber} after ${maxRetries} attempts. Final error: ${lastError?.message}`;
    await logger.error(finalErrorMessage, lastError);
    console.error(`🚫 ${finalErrorMessage}`);

    const errorResult: ExtractionResult = {
      timestamp: new Date().toISOString(),
      pageNumber,
      totalEntries: 0,
      listings: [],
      processingTime: 0,
      errors: [finalErrorMessage],
    };

    return errorResult;
  }

  private handleMultiPageEntries(
    listings: BusinessListing[],
    pageNumber: number
  ): BusinessListing[] {
    // This is a simplified implementation
    // In a more sophisticated version, you would:
    // 1. Check previous page results for incomplete entries
    // 2. Merge data from entries that span multiple pages
    // 3. Update continuation flags

    return listings.map(listing => {
      // Ensure page number is set correctly
      listing.pageNumber = pageNumber;

      // Basic heuristic: if confidence is low and text seems incomplete,
      // mark as potentially spanning pages
      if (listing.confidence < 0.7) {
        const businessName = listing.data.business_name || listing.data.name || '';
        if (typeof businessName === 'string' && businessName.length < 3) {
          listing.spanMultiplePages = true;
        }
      }

      return listing;
    });
  }

  private async saveBatchResults(results: ExtractionResult[]): Promise<void> {
    const processingConfig = this.config.getProcessingConfig();
    const extractedDir = join(processingConfig.outputDir, 'extracted');
    await FileUtils.ensureDirectoryExists(extractedDir);

    for (const result of results) {
      const filename = `page_${result.pageNumber.toString().padStart(3, '0')}.json`;
      const filePath = join(extractedDir, filename);
      await FileUtils.writeJsonFile(filePath, result);
    }

    await logger.debug(`Saved ${results.length} extraction results`);
  }

  async getAllExtractionResults(): Promise<ExtractionResult[]> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const extractedDir = join(processingConfig.outputDir, 'extracted');

      if (!await FileUtils.fileExists(extractedDir)) {
        return [];
      }

      const files = await FileUtils.listFiles(extractedDir, '.json');
      const results: ExtractionResult[] = [];

      for (const file of files) {
        try {
          const result = await FileUtils.readJsonFile<ExtractionResult>(file);
          results.push(result);
        } catch (error) {
          await logger.warn(`Failed to load extraction result: ${file}`, error);
        }
      }

      // Sort by page number
      results.sort((a, b) => a.pageNumber - b.pageNumber);

      return results;
    } catch (error) {
      await logger.error('Failed to load extraction results', error);
      return [];
    }
  }

  async getExtractionStatistics(): Promise<{
    totalPages: number;
    totalListings: number;
    averageListingsPerPage: number;
    pagesWithErrors: number;
    averageConfidence: number;
  }> {
    const results = await this.getAllExtractionResults();

    if (results.length === 0) {
      return {
        totalPages: 0,
        totalListings: 0,
        averageListingsPerPage: 0,
        pagesWithErrors: 0,
        averageConfidence: 0,
      };
    }

    const totalListings = results.reduce((sum, result) => sum + result.totalEntries, 0);
    const pagesWithErrors = results.filter(result => result.errors && result.errors.length > 0).length;

    const allListings = results.flatMap(result => result.listings);
    const averageConfidence = allListings.length > 0
      ? allListings.reduce((sum, listing) => sum + listing.confidence, 0) / allListings.length
      : 0;

    return {
      totalPages: results.length,
      totalListings,
      averageListingsPerPage: totalListings / results.length,
      pagesWithErrors,
      averageConfidence,
    };
  }
}

// CLI function for direct usage
export async function runExtraction(): Promise<void> {
  const extractor = new ExtractUtility();

  try {
    await extractor.init();
    await extractor.extract();

    const stats = await extractor.getExtractionStatistics();

    console.log('\n📊 Extraction Results:');
    console.log(`📄 Pages processed: ${stats.totalPages}`);
    console.log(`📋 Total listings extracted: ${stats.totalListings}`);
    console.log(`📈 Average listings per page: ${stats.averageListingsPerPage.toFixed(1)}`);
    console.log(`❌ Pages with errors: ${stats.pagesWithErrors}`);
    console.log(`🎯 Average confidence: ${(stats.averageConfidence * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('❌ Extraction failed:', error);
    Deno.exit(1);
  }
}
