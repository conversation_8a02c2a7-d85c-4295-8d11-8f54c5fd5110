{"timestamp": "2025-06-11T22:21:40.642Z", "pagesAnalyzed": [56, 69, 224, 241, 250], "totalPages": 258, "columns": [{"name": "Business Name", "description": "The name of the business.", "dataType": "string", "required": true, "examples": ["Global Glasshouse Techniques", "Green Automation", "<PERSON><PERSON><PERSON>"]}, {"name": "Category", "description": "The industry or type of business.", "dataType": "string", "required": true, "examples": ["Greenhouse Technique", "Greenhouse Automation", "Crop Protection"]}, {"name": "Address", "description": "The street address of the business.", "dataType": "address", "required": true, "examples": ["6600 SZENTES, Hungary", "231 County Rd 34 E, COTTONWOOD, (ON) CANADA", "PO Box 244190 CALEDON, MALMSTEN, Netherlands"]}, {"name": "Mailing Address", "description": "The mailing address of the business (may be different from the street address).", "dataType": "address", "required": false, "examples": []}, {"name": "Phone Number", "description": "The main phone number of the business.", "dataType": "phone", "required": true, "examples": ["+36 62 609 422", "****** 784 7334", "+31 14 400 352"]}, {"name": "Fax Number", "description": "The fax number of the business.", "dataType": "phone", "required": false, "examples": ["+36 62 609 421", "****** 754 7334", "+44 140 2725"]}, {"name": "Email Address", "description": "The main email address of the business.", "dataType": "email", "required": true, "examples": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, {"name": "Website URL", "description": "The website address of the business.", "dataType": "url", "required": false, "examples": ["www.globalglasshouse.com", "www.green-automation.com", "www.keesgreeve.nl"]}, {"name": "Contact Person", "description": "The name of the contact person at the business.", "dataType": "string", "required": false, "examples": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"name": "Year Founded", "description": "The year the business was founded.", "dataType": "number", "required": false, "examples": ["1989", "1940", "1970"]}, {"name": "Staff Count", "description": "The number of employees at the business.", "dataType": "number", "required": false, "examples": ["200", "200", "261"]}, {"name": "Social Media Accounts", "description": "Links to the business's social media profiles.", "dataType": "string", "required": false, "examples": []}, {"name": "Brand/Franchise Info", "description": "Information about the brand or franchise the business belongs to.", "dataType": "string", "required": false, "examples": ["Green-V company", "Brand: <PERSON> Search, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "A GREEN-V company"]}, {"name": "Service Areas", "description": "The geographical areas served by the business.", "dataType": "string", "required": false, "examples": []}, {"name": "Specialties", "description": "Specific areas of expertise or services offered by the business.", "dataType": "string", "required": false, "examples": []}, {"name": "Awards", "description": "Awards or recognitions received by the business.", "dataType": "string", "required": false, "examples": []}, {"name": "Membership Information", "description": "Information about memberships in professional organizations.", "dataType": "string", "required": false, "examples": ["Chamber of Commerce", "Chamber of Commerce", "Chamber of Commerce"]}, {"name": "Hours of Operation", "description": "The business's operating hours.", "dataType": "string", "required": false, "examples": []}, {"name": "Certifications", "description": "Certifications or accreditations held by the business.", "dataType": "string", "required": false, "examples": []}], "confidence": 1, "notes": "Analysis based on 5 random pages from 258 total pages"}